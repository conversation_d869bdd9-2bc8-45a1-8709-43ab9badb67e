using AvrStream.Vms.Entities.Configurations;
using AvrStream.Vms.Entities.Models;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Vms.Entities.Databases;

public sealed class VmsDbContext : DbContext
{
    public VmsDbContext(DbContextOptions<VmsDbContext> options) : base(options)
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        Database.Migrate();
    }

    public DbSet<Box> Boxes { get; set; }
    public DbSet<RecordingFile> RecordingFiles { get; set; }
    public DbSet<CameraViewSetting> Settings { get; set; }
    public DbSet<CameraDashboard> CameraDashboards { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.ApplyConfiguration(new BoxConfiguration());
        modelBuilder.ApplyConfiguration(new RecordingFileConfiguration());
        modelBuilder.ApplyConfiguration(new CameraViewSettingConfiguration());
        modelBuilder.ApplyConfiguration(new CameraDashboardConfiguration());
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedDate = DateTime.Now;
                    break;
                case EntityState.Modified:
                    entry.Entity.LastUpdatedDate = DateTime.Now;
                    break;
            }
        }
        return base.SaveChangesAsync(cancellationToken);
    }
}