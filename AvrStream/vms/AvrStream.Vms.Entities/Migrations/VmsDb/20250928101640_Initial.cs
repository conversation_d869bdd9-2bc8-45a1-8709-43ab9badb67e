using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AvrStream.Vms.Entities.Migrations.VmsDb
{
    public partial class Initial : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Boxes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UniqueId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IpAddress = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UrlStream1 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UrlStream2 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Username = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Password = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Boxes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CameraDashboards",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Row = table.Column<int>(type: "integer", nullable: false),
                    Column = table.Column<int>(type: "integer", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    CameraDashboardItems = table.Column<string>(type: "character varying(100000)", maxLength: 100000, nullable: true),
                    TextDefaultConfiguration = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CameraDashboards", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RecordingFiles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Password = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Iv = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsEncrypt = table.Column<bool>(type: "boolean", nullable: false),
                    Path = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    DecryptedFilePath = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    Hash = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    RecordingDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    BoxId = table.Column<Guid>(type: "uuid", nullable: false),
                    BoxUniqueId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecordingFiles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CameraViewSettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UniqueId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UrlStream = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    BoxId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CameraViewSettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CameraViewSettings_Boxes_BoxId",
                        column: x => x.BoxId,
                        principalTable: "Boxes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CameraViewSettings_BoxId",
                table: "CameraViewSettings",
                column: "BoxId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CameraDashboards");

            migrationBuilder.DropTable(
                name: "CameraViewSettings");

            migrationBuilder.DropTable(
                name: "RecordingFiles");

            migrationBuilder.DropTable(
                name: "Boxes");
        }
    }
}
