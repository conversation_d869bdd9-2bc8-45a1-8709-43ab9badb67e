using AvrStream.Vms.Entities.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AvrStream.Vms.Entities.Configurations;

public class CameraDashboardConfiguration : IEntityTypeConfiguration<CameraDashboard>
{
    public void Configure(EntityTypeBuilder<CameraDashboard> builder)
    {
        builder.ToTable("CameraDashboards");
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).ValueGeneratedOnAdd();
        builder.Property(x => x.Name).HasMaxLength(500);
        builder.Property(x => x.Description).HasMaxLength(5000);
        builder.Property(x => x.UserId).HasMaxLength(100);
        builder.Property(x => x.CameraDashboardItems).HasMaxLength(100000);
        builder.Property(x => x.TextDefaultConfiguration).HasMaxLength(10000);
    }
}