<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.35" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.35" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.35">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.29" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\VmsDb\" />
  </ItemGroup>

</Project>
