using System.Security.Claims;
using System.Text;
using AvrStream.Base.Jwt;
using AvrStream.Vms.Api.Hub;
using AvrStream.Vms.Api.Securities;
using AvrStream.Vms.Api.SeedData;
using AvrStream.Vms.Api.Services;
using AvrStream.Vms.Api.Tus;
using AvrStream.Vms.Api.Workers;
using AvrStream.Vms.Entities;
using AvrStream.Vms.Entities.Databases;
using AvrStream.Vms.Entities.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using tusdotnet;
using tusdotnet.Models.Configuration;
using tusdotnet.Models;
using tusdotnet.Stores;

var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .CreateLogger();

builder.Logging.AddSerilog(logger);
services.AddControllers();
services.AddUserEntities(builder.Configuration.GetConnectionString("VmsUser"));
services.AddVmsEntities(builder.Configuration.GetConnectionString("Vms"));
services.AddIdentity<AppUser, IdentityRole>((options =>
    {
        options.Password.RequireDigit = false;
        options.Password.RequiredLength = 5;
        options.Password.RequireLowercase = false;
        options.Password.RequireUppercase = false;
        options.Password.RequireNonAlphanumeric = false;
        options.SignIn.RequireConfirmedAccount = false;
    }))
    .AddEntityFrameworkStores<UserDbContext>()
    .AddDefaultTokenProviders();
services.AddCors(options =>
{
    options.AddDefaultPolicy(
        corsPolicyBuilder =>
        {
            corsPolicyBuilder
                .AllowAnyMethod()
                .AllowAnyHeader()
                .SetIsOriginAllowed(_ => true)
                .AllowCredentials();
        });
});
services.AddSignalR();
services.AddScoped<Jwt>();
services.AddScoped<Token>();
services.AddScoped<ClientToken>();
services.AddScoped<AccountService>();
services.AddScoped<BoxService>();
services.AddScoped<RoleService>();
services.AddScoped<CameraDashboardService>();


services.AddSingleton<AesFileDecryption>();
services.AddSingleton<ResourceService>();
services.AddSingleton<RecordingFileService>();

//services.AddHostedService<SystemInfoWorker>();
//services.AddHostedService<CleaningService>();
services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        ValidateIssuer = false,
        ValidateLifetime = true,
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? throw new InvalidOperationException())),
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
})
.AddJwtBearer("ClientAuth", options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        ValidateIssuer = true,
        ValidIssuer = builder.Configuration.GetValue<string>("ClientJwt:Issuer"),
        ValidateLifetime = false,
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["ClientJwt:Key"] ?? throw new InvalidOperationException())),
        ValidateAudience = true,
        ValidAudience = builder.Configuration.GetValue<string>("ClientJwt:Audience")
    };
});
services.AddAuthorization(options =>
{
    //Account
    options.AddPolicy("CreateAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("CreateAccount", new string[] { "True" });
    });
    options.AddPolicy("UpdateAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdateAccount", new string[] { "True" });
    });
    options.AddPolicy("DeleteAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("DeleteAccount", new string[] { "True" });
    });
    options.AddPolicy("GetAccount", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetAccount", new string[] { "True" });
    });

    options.AddPolicy("ResetPassword", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("ResetPassword", new string[] { "True" });
    });

    options.AddPolicy("UpdatePermission", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdatePermission", new string[] { "True" });
    });

    //Role

    options.AddPolicy("CreateRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("CreateRole", new string[] { "True" });
    });
    options.AddPolicy("UpdateRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdateRole", new string[] { "True" });
    });
    options.AddPolicy("DeleteRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("DeleteRole", new string[] { "True" });
    });
    options.AddPolicy("GetRole", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetRole", new string[] { "True" });
    });

    // Box

    options.AddPolicy("GetBox", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetBox", new string[] { "True" });
    });

    options.AddPolicy("UpdateBox", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("UpdateBox", new string[] { "True" });
    });

    options.AddPolicy("DeleteBox", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("DeleteBox", new string[] { "True" });
    });

    // Recording File
    options.AddPolicy("GetRecordingFile", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("GetRecordingFile", new string[] { "True" });
    });

    options.AddPolicy("DeleteRecordingFile", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("DeleteRecordingFile", new string[] { "True" });
    });

    // View Camera

    options.AddPolicy("ViewCamera", policy =>
    {
        policy.RequireAuthenticatedUser();
        policy.RequireClaim("ViewCamera", new string[] { "True" });
    });
});
var app = builder.Build();
app.UseCors();
app.UseAuthentication();
app.UseAuthorization();

app.UseTus(ctx =>
{
    var tusUploadPath = builder.Configuration.GetValue<string>("TempUploadPath");
    Directory.CreateDirectory(tusUploadPath);
    var sessionFileIdProvider = new SessionFileIdProvider();
    return new DefaultTusConfiguration
    {
        Store = new TusDiskStore(tusUploadPath, true, TusDiskBufferSize.Default, sessionFileIdProvider),
        UrlPath = "/files",
        FileLockProvider = new AvrFileLockProvider(),
        Events = new Events
        {
            // authorize tus request via scheme: ClientAuth
            OnAuthorizeAsync = async e =>
            {
                var res = await e.HttpContext.AuthenticateAsync("ClientAuth");
                if (res.Principal is { } principal) e.HttpContext.User = principal;
            },
            OnBeforeCreateAsync = c =>
            {
                if (c.Metadata.TryGetValue("uniqueId", out var metadata))
                {
                    var uniqueId = metadata.GetString(Encoding.Default);
                    if (uniqueId != null && $"{uniqueId}" ==
                        c.HttpContext.User.FindFirstValue(ClaimTypes.NameIdentifier))
                        return Task.CompletedTask;
                }
                c.FailRequest("ClientId not valid");
                return Task.CompletedTask;
            },
            OnFileCompleteAsync = async e =>
            {
                var srcFile = Path.Combine(tusUploadPath, e.FileId);
                using var scope = ctx.RequestServices.CreateScope();
                var loggerProgram = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
                var recordingFileService = scope.ServiceProvider.GetRequiredService<RecordingFileService>();
                loggerProgram.LogInformation($"srcFile: {srcFile}");
                var (recordingFileId, fileName) = sessionFileIdProvider.DestructFileId(e.FileId);
                loggerProgram.LogInformation($"Upload file: {fileName}");
                var recordingFile = await recordingFileService.GetRecordingFileAsync(recordingFileId);
                if (recordingFile == null)
                {
                    File.Delete(srcFile);
                }
                else
                {
                    // move to session's data folder
                    var dstFile = recordingFile.Path;
                    File.Move(srcFile, dstFile, true);
                    loggerProgram.LogInformation($"Uploaded File Successful: srcFile: {srcFile}, fileName: {fileName}");
                }
            },
            OnBeforeDeleteAsync = e =>
            {
                e.FailRequest("Not allow to remove file");
                return Task.CompletedTask;
            }
        }
    };
});
app.MapControllers();
app.MapHub<DashboardHub>("/DashboardHub");
app.Lifetime.ApplicationStarted.Register(() =>
{
    var factory = app.Services.GetRequiredService<IServiceScopeFactory>();
    using var scope = factory.CreateScope();
    var userDb = scope.ServiceProvider.GetRequiredService<UserDbContext>();
    if (userDb.Database.IsRelational())
    {
        userDb.Database.Migrate();
        var userManager = scope.ServiceProvider.GetRequiredService<UserManager<AppUser>>();
        var userLogger = scope.ServiceProvider.GetRequiredService<ILogger<UserDbContextSeed>>();
        var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
        UserDbContextSeed.SeedAsync(userDb, roleManager, userManager, userLogger).Wait();
    }
    var vmsDb = scope.ServiceProvider.GetRequiredService<VmsDbContext>();
    if (vmsDb.Database.IsRelational())
    {
        vmsDb.Database.Migrate();
    }
});
app.Run();
