using System.Text.Json;
using AvrStream.Base.Models;
using AvrStream.Vms.Api.Models;
using AvrStream.Vms.Api.Models.Requests;
using AvrStream.Vms.Api.Models.Responses;
using AvrStream.Vms.Entities.Databases;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Vms.Api.Services;

public class CameraDashboardService
{
    private readonly VmsDbContext _dbContext;
    public CameraDashboardService(VmsDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<ListResult<CameraDashboardInfo>> ListCameraDashboardInfosAsync(ListCameraDashboardRequest request)
    {
        var query = _dbContext.CameraDashboards.Where(cd => cd.UserId == request.UserId &&
                                                            (string.IsNullOrEmpty(request.Name) || EF.Functions.ToTsVector("english",cd.Name).Matches(request.Name)));
        var total = await query.CountAsync();
        var cameraDashboards = await query
            .OrderBy(cd => cd.Name)
            .Skip((request.Page - 1) * request.Take)
            .Take(request.Take)
            .ToListAsync();
        var cameraDashboardItems = new List<CameraDashboardInfo>();
        cameraDashboards.ForEach(cd =>
        {
            var items = JsonSerializer.Deserialize<List<CameraDashboardItem>>(cd.CameraDashboardItems);
            var textConfig = JsonSerializer.Deserialize<CameraTextConfiguration>(cd.TextDefaultConfiguration);
            cameraDashboardItems.Add(new CameraDashboardInfo
            {
                Id = cd.Id,
                Name = cd.Name,
                Description = cd.Description,
                UserId = cd.UserId,
                Row = cd.Row,
                Column = cd.Column,
                CameraDashboardItems = items ?? new List<CameraDashboardItem>(),
                TextDefaultConfiguration = textConfig ?? new CameraTextConfiguration()
            });
        });
        return new ListResult<CameraDashboardInfo>(cameraDashboardItems, total);
    }

    public async Task<CameraDashboardInfo> GetCameraDashboardInfoAsync(Guid id)
    {
        var cd = await _dbContext.CameraDashboards
            .SingleOrDefaultAsync(cd => cd.Id == id);
        if (cd == null)
        {
            return null;
        }
        var items = JsonSerializer.Deserialize<List<CameraDashboardItem>>(cd.CameraDashboardItems);
        var textConfig = JsonSerializer.Deserialize<CameraTextConfiguration>(cd.TextDefaultConfiguration);
        return new CameraDashboardInfo
        {
            Id = cd.Id,
            Name = cd.Name,
            Description = cd.Description,
            UserId = cd.UserId,
            Row = cd.Row,
            Column = cd.Column,
            CameraDashboardItems = items ?? new List<CameraDashboardItem>(),
            TextDefaultConfiguration = textConfig ?? new CameraTextConfiguration()
        };
    }

    public async Task<BaseResponse> CreateOrUpdateCameraDashboardAsync(CreateOrUpdateCameraDashboardRequest request)
    {
        if (request == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Invalid camera dashboard info"
            };
        }
        if (string.IsNullOrEmpty(request.UserId))
        {
            return new BaseResponse
            {
                Success = false,
                Message = "UserId is required"
            };
        }
        if (string.IsNullOrEmpty(request.Name))
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Name is required"
            };
        }
        if (request.Row <= 0 || request.Column <= 0)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Row and Column must be greater than 0"
            };
        }
        var existingDashboard = await _dbContext.CameraDashboards
            .FirstOrDefaultAsync(cd => cd.Id == request.Id);
        if (existingDashboard == null)
        {
            var newDashboard = new Entities.Models.CameraDashboard
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Description = request.Description,
                UserId = request.UserId,
                Row = request.Row,
                Column = request.Column,
                CameraDashboardItems = JsonSerializer.Serialize(request.CameraDashboardItems),
                TextDefaultConfiguration = JsonSerializer.Serialize(request.TextDefaultConfiguration)
            };
            await _dbContext.CameraDashboards.AddAsync(newDashboard);
        }
        else
        {
            existingDashboard.Name = request.Name;
            existingDashboard.Description = request.Description;
            existingDashboard.Row = request.Row;
            existingDashboard.Column = request.Column;
            existingDashboard.CameraDashboardItems = JsonSerializer.Serialize(request.CameraDashboardItems);
            existingDashboard.TextDefaultConfiguration = JsonSerializer.Serialize(request.TextDefaultConfiguration);
            _dbContext.CameraDashboards.Update(existingDashboard);
        }
        await _dbContext.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = "Camera dashboard saved successfully"
        };
    }

    public async Task<BaseResponse> DeleteCameraDashboardAsync(Guid id)
    {
        var dashboard = await _dbContext.CameraDashboards
            .FirstOrDefaultAsync(cd => cd.Id == id);
        if (dashboard == null)
        {
            return new BaseResponse
            {
                Success = false,
                Message = "Camera dashboard not found"
            };
        }
        _dbContext.CameraDashboards.Remove(dashboard);
        await _dbContext.SaveChangesAsync();
        return new BaseResponse
        {
            Success = true,
            Message = "Camera dashboard deleted successfully"
        };
    }
}